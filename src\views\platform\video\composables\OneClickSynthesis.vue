<template>
  <div class="one-click-synthesis">
    <div class="panel-header">
      <div class="header-info">
        <h3 class="panel-title">
          <el-icon class="panel-icon"><VideoCamera /></el-icon>
          一键合成
        </h3>
        <p class="panel-desc">选择合成版本并配置相关参数，点击开始合成即可完成整个流程</p>
      </div>
    </div>

    <div class="synthesis-content">
      <!-- 版本选择 -->
      <div class="version-section">
        <h4 class="section-title">选择合成版本</h4>
        <div class="version-grid">
          <div v-for="version in versions" :key="version.value" class="version-card" :class="{ active: synthesisConfig.version === version.value }" @click="selectVersion(version.value)">
            <div class="version-icon">
              <el-icon><VideoPlay /></el-icon>
            </div>
            <h5 class="version-name">{{ version.name }}</h5>
            <p class="version-desc">{{ version.description }}</p>
            <div class="version-features">
              <span v-for="feature in version.features" :key="feature" class="feature-tag">
                {{ feature }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 版本特定配置 -->
      <div v-if="synthesisConfig.version" class="config-section">
        <h4 class="section-title">{{ getVersionName(synthesisConfig.version) }} 配置</h4>
        
        <!-- M版配置 -->
        <div v-if="synthesisConfig.version === 'M'" class="config-panel">
          <div class="config-item">
            <label class="config-label">阈值设置 (bboxShiftValue)</label>
            <div class="slider-container">
              <el-slider
                v-model="synthesisConfig.bboxShiftValue"
                :min="-7"
                :max="7"
                :step="1"
                show-stops
                show-input
                class="config-slider"
                @change="updateConfig"
              />
            </div>
            <p class="config-tip">调整数字人面部区域的偏移量，范围：-7 到 +7</p>
          </div>
        </div>

        <!-- V版配置 -->
        <div v-if="synthesisConfig.version === 'V'" class="config-panel">
          <div class="config-item">
            <label class="config-label">选择模型</label>
            <el-select 
              v-model="synthesisConfig.model" 
              placeholder="请选择AI模型" 
              class="model-select"
              size="large"
              @change="updateConfig"
            >
              <el-option
                v-for="model in availableModels"
                :key="model.modelCode"
                :label="model.modelName"
                :value="model.modelCode"
                :disabled="model.modelStatus !== 1"
              >
                <div class="model-option">
                  <span class="model-name">{{ model.modelName }}</span>
                  <span class="model-status" :class="{ available: model.modelStatus === 1 }">
                    {{ model.modelStatus === 1 ? '可用' : '不可用' }}
                  </span>
                </div>
              </el-option>
            </el-select>
            <p class="config-tip">选择用于视频合成的AI模型，不同模型效果可能有所差异</p>
          </div>
        </div>

        <!-- H版配置 -->
        <div v-if="synthesisConfig.version === 'H'" class="config-panel">
          <div class="config-info">
            <el-icon class="info-icon"><InfoFilled /></el-icon>
            <div class="info-content">
              <h5>H版 - 高清画质</h5>
              <p>H版使用默认的高清配置，无需额外设置。将为您提供最佳的画质效果。</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 合成预览 -->
      <div class="preview-section">
        <h4 class="section-title">合成预览</h4>
        <div class="preview-grid">
          <div class="preview-item">
            <div class="preview-label">参与数字人</div>
            <div class="preview-value">{{ humanCount }} 个</div>
          </div>
          <div class="preview-item">
            <div class="preview-label">对话内容</div>
            <div class="preview-value">{{ dialogueCount }} 条</div>
          </div>
          <div class="preview-item">
            <div class="preview-label">合成版本</div>
            <div class="preview-value">{{ getVersionName(synthesisConfig.version) }}</div>
          </div>
          <div class="preview-item">
            <div class="preview-label">预计时长</div>
            <div class="preview-value">{{ estimatedDuration }} 秒</div>
          </div>
        </div>
      </div>

      <!-- 视频信息配置 -->
      <div class="video-info-section">
        <h4 class="section-title">视频信息</h4>
        <div class="video-info-form">
          <div class="form-row">
            <label class="form-label">视频标题</label>
            <el-input
              v-model="videoInfo.title"
              placeholder="请输入视频标题"
              maxlength="50"
              show-word-limit
              class="form-input"
            />
          </div>
          <div class="form-row">
            <label class="form-label">视频描述</label>
            <el-input
              v-model="videoInfo.description"
              type="textarea"
              :rows="3"
              placeholder="请输入视频描述（可选）"
              maxlength="200"
              show-word-limit
              class="form-textarea"
            />
          </div>
        </div>
      </div>

      <!-- 一键合成控制 -->
      <div class="synthesis-control">
        <div class="control-buttons">
          <el-button 
            type="primary" 
            size="large" 
            @click="startOneClickSynthesis" 
            :loading="synthesisStatus.isRunning" 
            :disabled="!canStart" 
            class="synthesis-btn"
          >
            <el-icon><VideoPlay /></el-icon>
            {{ getSynthesisButtonText() }}
          </el-button>
        </div>

        <!-- 合成状态显示 -->
        <div v-if="synthesisStatus.message" class="synthesis-status">
          <div class="status-card" :class="getStatusClass()">
            <div class="status-icon">
              <el-icon v-if="synthesisStatus.isRunning"><Loading /></el-icon>
              <el-icon v-else-if="synthesisStatus.isCompleted"><CircleCheck /></el-icon>
              <el-icon v-else-if="synthesisStatus.hasError"><CircleClose /></el-icon>
              <el-icon v-else><InfoFilled /></el-icon>
            </div>
            <div class="status-content">
              <h5 class="status-title">{{ synthesisStatus.title }}</h5>
              <p class="status-message">{{ synthesisStatus.message }}</p>
              <div v-if="synthesisStatus.progress" class="status-progress">
                <el-progress 
                  :percentage="synthesisStatus.progress" 
                  :status="synthesisStatus.isCompleted ? 'success' : (synthesisStatus.hasError ? 'exception' : null)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'

import { VideoCamera, VideoPlay, InfoFilled, Loading, CircleCheck, CircleClose } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { dialogueSynthesis, getAutoSynthesisStatus } from '@/api/platform/video'
import { listWymodel } from '@/api/platform/model'



const props = defineProps({
  digitalHumans: {
    type: Array,
    default: () => []
  },
  dialogueContent: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['synthesis-complete'])

// 组件内部状态
const availableModels = ref([])
const synthesisConfig = reactive({
  version: 'M',
  bboxShiftValue: 0,
  model: ''
})

const videoInfo = reactive({
  title: '',
  description: ''
})

const synthesisStatus = reactive({
  isRunning: false,
  isCompleted: false,
  hasError: false,
  title: '',
  message: '',
  progress: 0
})

const dialogueGroupId = ref('')
const finalResult = ref(null)

// 定时器
let statusCheckTimer = null

// 内置音色列表
const builtinVoices = ['zhiyuan', 'zhiyue', 'zhistella', 'zhida', 'aiqi', 'aicheng', 'aijia', 'siqi', 'sijia', 'mashu', 'yuer', 'ruoxi', 'aida', 'sicheng', 'ninger', 'xiaoyun', 'xiaogang', 'ruilin']

// 计算属性
const humanCount = computed(() => props.digitalHumans.length)
const dialogueCount = computed(() => props.dialogueContent.length)
const estimatedDuration = computed(() => {
  const avgWordsPerSecond = 3
  const totalWords = props.dialogueContent.reduce((sum, dialogue) => {
    return sum + (dialogue.text ? dialogue.text.length : 0)
  }, 0)
  return Math.max(10, Math.ceil(totalWords / avgWordsPerSecond))
})

const canStart = computed(() => {
  return synthesisConfig.version &&
         props.digitalHumans.length >= 2 &&
         props.dialogueContent.length > 0 &&
         (synthesisConfig.version !== 'V' || synthesisConfig.model) &&
         !synthesisStatus.isRunning &&
         videoInfo.title.trim()
})

const versions = [
  {
    value: 'M',
    name: 'M版',
    description: '标准版本，支持基础对话合成',
    features: ['标准画质', '快速合成', '阈值调节']
  },
  {
    value: 'V',
    name: 'V版',
    description: '增强版本，支持AI模型选择',
    features: ['AI增强', '模型选择', '优化效果']
  },
  {
    value: 'H',
    name: 'H版',
    description: '高清版本，提供最佳画质',
    features: ['高清画质', '专业效果', '默认配置']
  }
]

// 方法
const selectVersion = (version) => {
  synthesisConfig.version = version
  if (version === 'M') {
    synthesisConfig.bboxShiftValue = 0
  } else if (version === 'V') {
    synthesisConfig.model = ''
    loadAvailableModels()
  }
}

const loadAvailableModels = async () => {
  try {
    const response = await listWymodel()
    if (response && response.rows) {
      availableModels.value = response.rows.filter(model => model.modelStatus === 1)
      if (availableModels.value.length > 0 && !synthesisConfig.model) {
        synthesisConfig.model = availableModels.value[0].modelCode
      }
    }
  } catch (error) {
    console.error('加载模型失败:', error)
    ElMessage.error('加载模型失败')
  }
}

const updateConfig = () => {
  // 配置更新时的处理逻辑
}

const getVersionName = (version) => {
  const versionMap = {
    'M': 'M版 - 标准版',
    'V': 'V版 - 增强版',
    'H': 'H版 - 高清版'
  }
  return versionMap[version] || '未选择'
}

const getSynthesisButtonText = () => {
  if (synthesisStatus.isRunning) {
    return '合成中...'
  } else if (synthesisStatus.isCompleted) {
    return '重新合成'
  } else {
    return '开始合成'
  }
}

const getStatusClass = () => {
  if (synthesisStatus.isCompleted) return 'status-success'
  if (synthesisStatus.hasError) return 'status-error'
  if (synthesisStatus.isRunning) return 'status-running'
  return 'status-info'
}

// 一键合成主方法
const startOneClickSynthesis = async () => {
  if (!canStart.value) {
    ElMessage.warning('请完成所有配置后再开始合成')
    return
  }

  try {
    // 重置状态
    resetSynthesisStatus()

    // 重新初始化视频信息，确保获取最新数据
    initVideoInfo()

    synthesisStatus.isRunning = true
    synthesisStatus.title = '开始合成'
    synthesisStatus.message = '正在准备合成任务...'
    synthesisStatus.progress = 10

    // 验证数据
    validateSynthesisData()

    // 构建合成参数
    const synthesisParams = buildSynthesisParams()

    // 调用后端一键合成接口
    synthesisStatus.message = '正在提交合成任务...'
    synthesisStatus.progress = 20

    const response = await dialogueSynthesis(synthesisParams)

    if (response && response.data) {
      dialogueGroupId.value = response.data.dialogueGroupId

      synthesisStatus.title = '合成任务已提交'
      synthesisStatus.message = response.data.message || '正在处理合成任务...'
      synthesisStatus.progress = 30

      // 开始监控合成状态
      startStatusMonitoring()

      ElMessage.success('合成任务已提交，正在自动处理...')
      emit('synthesis-complete', response.data)
    } else {
      throw new Error('合成响应异常')
    }
  } catch (error) {
    console.error('一键合成失败:', error)
    synthesisStatus.isRunning = false
    synthesisStatus.hasError = true
    synthesisStatus.title = '合成失败'
    synthesisStatus.message = error.message || '未知错误'
    ElMessage.error('合成失败: ' + (error.message || '请稍后重试'))
  }
}

// 验证合成数据
const validateSynthesisData = () => {
  if (!synthesisConfig.version) {
    throw new Error('请选择合成版本')
  }

  if (props.digitalHumans.length < 2) {
    throw new Error('至少需要选择2个数字人')
  }

  if (props.dialogueContent.length === 0) {
    throw new Error('请添加对话内容')
  }

  if (synthesisConfig.version === 'V' && !synthesisConfig.model) {
    throw new Error('V版需要选择AI模型')
  }

  if (!videoInfo.title.trim()) {
    throw new Error('请输入视频标题')
  }

  // 验证对话内容
  props.dialogueContent.forEach((dialogue, index) => {
    if (!dialogue.text || dialogue.text.trim() === '') {
      throw new Error(`第${index + 1}条对话内容不能为空`)
    }
    if (!dialogue.speaker) {
      throw new Error(`第${index + 1}条对话缺少发言人`)
    }
  })

  // 验证数字人配置
  props.digitalHumans.forEach((human, index) => {
    if (!human.name || human.name.trim() === '') {
      throw new Error(`第${index + 1}个数字人缺少名称`)
    }
    if (!human.voiceId && !human.voiceName) {
      throw new Error(`第${index + 1}个数字人缺少声音配置`)
    }
  })
}

// 重置合成状态
const resetSynthesisStatus = () => {
  synthesisStatus.isRunning = false
  synthesisStatus.isCompleted = false
  synthesisStatus.hasError = false
  synthesisStatus.title = ''
  synthesisStatus.message = ''
  synthesisStatus.progress = 0
  finalResult.value = null
}

// 构建合成参数
const buildSynthesisParams = () => {
  return {
    version: synthesisConfig.version,
    autoSynthesis: true, // 启用自动合成
    title: videoInfo.title,
    description: videoInfo.description,
    digitalHumans: props.digitalHumans.map((human, index) => {
      // 验证必要字段
      if (!human.avatarAddress) {
        throw new Error(`数字人${index + 1}缺少形象地址`)
      }
      if (!human.name || human.name.trim() === '') {
        throw new Error(`数字人${index + 1}缺少名称`)
      }
      if (!human.voiceName) {
        throw new Error(`数字人${index + 1}缺少声音名称`)
      }

      // 处理声音类型和ID
      let voiceType = human.voiceType || 'builtin'
      let voiceId = null

      if (voiceType === 'system') {
        voiceId = parseInt(human.voiceId)
        if (!voiceId || voiceId <= 0) {
          throw new Error(`数字人${index + 1}的系统声音ID无效`)
        }
      } else if (voiceType === 'builtin') {
        if (!builtinVoices.includes(human.voiceName)) {
          throw new Error(`数字人${index + 1}使用了不支持的内置音色: ${human.voiceName}`)
        }
      }

      // 构建规范的数字人配置
      return {
        id: human.id,
        name: human.name.trim(),
        avatarAddress: human.avatarAddress,
        avatarName: human.avatarName,
        voiceType: voiceType,
        voiceId: voiceId,
        voiceName: human.voiceName
      }
    }),
    dialogueContent: props.dialogueContent.map((dialogue, index) => {
      // 验证对话内容
      if (!dialogue.text || dialogue.text.trim() === '') {
        throw new Error(`对话${index + 1}的文本内容不能为空`)
      }

      if (!dialogue.speaker) {
        throw new Error(`对话${index + 1}缺少发言人配置`)
      }

      // 验证发言人是否存在于数字人配置中
      const humanExists = props.digitalHumans.some(human =>
        human.id === dialogue.speaker
      )

      if (!humanExists) {
        throw new Error(`对话${index + 1}的发言人配置错误: ${dialogue.speaker}`)
      }

      // 构建规范的对话数据
      return {
        id: index + 1,
        speaker: dialogue.speaker, // 格式为 "human_imageId"
        speakerName: dialogue.speakerName || `数字人${index + 1}`,
        text: dialogue.text.trim(),
        order: index + 1 // 确保顺序字段始终有值，从1开始
      }
    }),
    bboxShiftValue: synthesisConfig.version === 'M' ? synthesisConfig.bboxShiftValue : null,
    model: synthesisConfig.version === 'V' ? synthesisConfig.model : null
  }
}

// 开始状态监控
const startStatusMonitoring = () => {
  // 立即检查一次
  checkAutoSynthesisStatus()

  // 每10秒检查一次状态
  statusCheckTimer = setInterval(() => {
    if (!synthesisStatus.isCompleted && !synthesisStatus.hasError) {
      checkAutoSynthesisStatus()
    } else {
      stopStatusMonitoring()
    }
  }, 10000)
}

// 停止状态监控
const stopStatusMonitoring = () => {
  if (statusCheckTimer) {
    clearInterval(statusCheckTimer)
    statusCheckTimer = null
  }
}

// 检查自动合成状态
const checkAutoSynthesisStatus = async () => {
  if (!dialogueGroupId.value) {
    return
  }

  try {
    const response = await getAutoSynthesisStatus(dialogueGroupId.value)

    if (response.code === 200 && response.data) {
      const status = response.data

      // 更新进度
      updateSynthesisProgress(status)

      // 检查是否完成 - 必须所有任务完成且云剪辑完成
      const isFullyCompleted = (status.overallStatus === 'FULLY_COMPLETED') ||
                               (status.overallStatus === 'DIALOGUE_COMPLETED' && status.alreadyClipped)

      if (isFullyCompleted) {
        synthesisStatus.isRunning = false
        synthesisStatus.isCompleted = true
        synthesisStatus.title = '合成完成'
        synthesisStatus.message = '数字人对话视频合成完成！'
        synthesisStatus.progress = 100

        finalResult.value = {
          dialogueGroupId: dialogueGroupId.value,
          finalVideoUrl: status.clipInfo?.finalVideoUrl,
          clipInfo: status.clipInfo
        }

        stopStatusMonitoring()
        ElMessage.success('数字人对话视频合成完成！')
      } else if (status.overallStatus === 'FAILED') {
        synthesisStatus.isRunning = false
        synthesisStatus.hasError = true
        synthesisStatus.title = '合成失败'
        synthesisStatus.message = status.message || '合成过程中出现错误'

        stopStatusMonitoring()
        ElMessage.error('合成失败: ' + (status.message || '请稍后重试'))
      } else {
        // 更新状态信息
        synthesisStatus.title = getStatusTitle(status.overallStatus)
        synthesisStatus.message = status.message || getStatusMessage(status)
      }
    } else {
      throw new Error(response.msg || '检查状态失败')
    }
  } catch (error) {
    console.error('检查自动合成状态失败:', error)
    // 不立即停止监控，可能是网络问题
  }
}

// 更新合成进度 - 简化版本
const updateSynthesisProgress = (status) => {
  // 完成状态
  if (status.overallStatus === 'FULLY_COMPLETED' ||
      (status.overallStatus === 'DIALOGUE_COMPLETED' && status.alreadyClipped)) {
    synthesisStatus.progress = 100
    return
  }

  // 计算基础进度
  let progress = 20
  if (status.totalTasks && status.completedTasks !== undefined) {
    const taskProgress = (status.completedTasks / status.totalTasks) * 70
    progress += taskProgress

    // 如果任务完成但未云剪辑，显示90%
    if (status.completedTasks === status.totalTasks && !status.alreadyClipped) {
      progress = 90
    }
  }

  synthesisStatus.progress = Math.min(progress, 95)
}

// 获取状态标题 - 简化版本
const getStatusTitle = (overallStatus) => {
  if (overallStatus === 'FULLY_COMPLETED' || overallStatus === 'DIALOGUE_COMPLETED') {
    return '合成完成'
  }
  if (overallStatus === 'FAILED') {
    return '合成失败'
  }
  return '处理中'
}

// 获取状态消息 - 简化版本
const getStatusMessage = (status) => {
  // 完成状态
  if (status.overallStatus === 'FULLY_COMPLETED' ||
      (status.overallStatus === 'DIALOGUE_COMPLETED' && status.alreadyClipped)) {
    return '数字人对话视频合成完成！'
  }

  // 处理中状态 - 优先使用根级别任务状态
  if (status.totalTasks !== undefined && status.completedTasks !== undefined) {
    const { totalTasks, completedTasks, failedTasks = 0 } = status
    if (failedTasks > 0) {
      return `处理中，已完成 ${completedTasks}/${totalTasks}，${failedTasks} 个失败`
    }
    if (completedTasks === totalTasks && !status.alreadyClipped) {
      return '视频合成完成，正在进行云剪辑...'
    }
    return `处理中，已完成 ${completedTasks}/${totalTasks}`
  }

  return '正在处理中...'
}

// 初始化视频信息
const initVideoInfo = () => {
  const currentTime = new Date().toLocaleString('zh-CN')
  videoInfo.title = `数字人对话视频_${currentTime}`

  // 动态生成描述，确保获取到最新的数据
  const dialogueCount = props.dialogueContent?.length || 0
  const humanCount = props.digitalHumans?.length || 0
  videoInfo.description = `包含${dialogueCount}条对话的数字人视频，参与数字人：${humanCount}个`
}

// 生命周期
onMounted(() => {
  // 初始化视频信息
  initVideoInfo()

  // 如果是V版，加载模型
  if (synthesisConfig.version === 'V') {
    loadAvailableModels()
  }
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopStatusMonitoring()
})
</script>

<style lang="scss" scoped>
.one-click-synthesis {
  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding: 24px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 16px;
    border: 1px solid #e9ecef;

    .header-info {
      .panel-title {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 24px;
        font-weight: 700;
        color: #333;
        margin: 0 0 8px 0;

        .panel-icon {
          font-size: 28px;
          color: #667eea;
        }
      }

      .panel-desc {
        font-size: 16px;
        color: #666;
        margin: 0;
        line-height: 1.5;
      }
    }
  }

  .synthesis-content {
    .section-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 0 0 20px 0;
      display: flex;
      align-items: center;
      gap: 8px;

      &::before {
        content: '';
        width: 4px;
        height: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 2px;
      }
    }

    .version-section {
      margin-bottom: 40px;

      .version-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 20px;
        margin-top: 20px;
      }

      .version-card {
        background: white;
        border: 2px solid #e9ecef;
        border-radius: 16px;
        padding: 24px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 4px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          transform: scaleX(0);
          transition: transform 0.3s ease;
        }

        &:hover {
          border-color: #667eea;
          transform: translateY(-4px);
          box-shadow: 0 8px 20px rgba(102, 126, 234, 0.2);

          &::before {
            transform: scaleX(1);
          }
        }

        &.active {
          border-color: #667eea;
          background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);

          &::before {
            transform: scaleX(1);
          }

          .version-icon {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
          }
        }

        .version-icon {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          background: #f8f9fa;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          color: #667eea;
          margin-bottom: 16px;
          transition: all 0.3s ease;
        }

        .version-name {
          font-size: 20px;
          font-weight: 600;
          color: #333;
          margin: 0 0 8px 0;
        }

        .version-desc {
          font-size: 14px;
          color: #666;
          margin: 0 0 16px 0;
          line-height: 1.5;
        }

        .version-features {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;

          .feature-tag {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            color: #667eea;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            border: 1px solid rgba(102, 126, 234, 0.2);
          }
        }
      }
    }

    .config-section {
      margin-bottom: 40px;

      .config-panel {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 16px;
        padding: 24px;
        border: 1px solid #e9ecef;

        .config-item {
          margin-bottom: 24px;

          &:last-child {
            margin-bottom: 0;
          }

          .config-label {
            display: block;
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
          }

          .slider-container {
            margin-bottom: 12px;

            .config-slider {
              :deep(.el-slider__runway) {
                background: #e9ecef;
              }

              :deep(.el-slider__bar) {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              }

              :deep(.el-slider__button) {
                border-color: #667eea;
              }
            }
          }

          .model-select {
            width: 100%;
            margin-bottom: 12px;

            :deep(.el-select__wrapper) {
              border-radius: 12px;
            }
          }

          .config-tip {
            font-size: 14px;
            color: #666;
            margin: 0;
            line-height: 1.5;
          }
        }

        .config-info {
          display: flex;
          align-items: flex-start;
          gap: 16px;
          background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
          border: 1px solid rgba(102, 126, 234, 0.2);
          border-radius: 12px;
          padding: 20px;

          .info-icon {
            font-size: 24px;
            color: #667eea;
            flex-shrink: 0;
          }

          .info-content {
            h5 {
              font-size: 16px;
              font-weight: 600;
              color: #333;
              margin: 0 0 8px 0;
            }

            p {
              font-size: 14px;
              color: #666;
              margin: 0;
              line-height: 1.5;
            }
          }
        }
      }
    }

    .preview-section, .video-info-section {
      margin-bottom: 40px;

      .preview-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        margin-top: 20px;
      }

      .preview-item {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        padding: 20px;
        text-align: center;
        transition: all 0.3s ease;

        &:hover {
          border-color: #667eea;
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
        }

        .preview-label {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .preview-value {
          font-size: 20px;
          font-weight: 600;
          color: #333;
        }
      }

      .video-info-form {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        padding: 24px;
        margin-top: 20px;

        .form-row {
          margin-bottom: 20px;

          &:last-child {
            margin-bottom: 0;
          }

          .form-label {
            display: block;
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
          }

          .form-input,
          .form-textarea {
            width: 100%;
            border-radius: 8px;
          }
        }
      }
    }

    .synthesis-control {
      margin-bottom: 40px;
      text-align: center;

      .control-buttons {
        display: flex;
        justify-content: center;
        gap: 16px;
        margin-bottom: 24px;

        .synthesis-btn {
          background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
          border: none;
          border-radius: 12px;
          padding: 16px 32px;
          font-size: 16px;
          font-weight: 600;
          transition: all 0.3s ease;

          &:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(103, 194, 58, 0.3);
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;
          }
        }
      }

      .synthesis-status {
        margin-top: 24px;

        .status-card {
          display: flex;
          align-items: flex-start;
          gap: 12px;
          padding: 16px 20px;
          border-radius: 12px;
          border: 1px solid;
          background: white;

          &.status-running {
            border-color: #409eff;
            background: linear-gradient(135deg, rgba(64, 158, 255, 0.05) 0%, rgba(64, 158, 255, 0.08) 100%);
          }

          &.status-success {
            border-color: #67c23a;
            background: linear-gradient(135deg, rgba(103, 194, 58, 0.05) 0%, rgba(103, 194, 58, 0.1) 100%);
          }

          &.status-error {
            border-color: #f56c6c;
            background: linear-gradient(135deg, rgba(245, 108, 108, 0.05) 0%, rgba(245, 108, 108, 0.1) 100%);
          }

          &.status-info {
            border-color: #409eff;
            background: linear-gradient(135deg, rgba(64, 158, 255, 0.05) 0%, rgba(64, 158, 255, 0.1) 100%);
          }

          .status-icon {
            font-size: 24px;
            flex-shrink: 0;
            margin-top: 2px;

            .status-running & {
              color: #409eff;
            }

            .status-success & {
              color: #67c23a;
            }

            .status-error & {
              color: #f56c6c;
            }

            .status-info & {
              color: #409eff;
            }
          }

          .status-content {
            flex: 1;

            .status-title {
              font-size: 16px;
              font-weight: 600;
              color: #333;
              margin: 0 0 6px 0;
            }

            .status-message {
              font-size: 13px;
              color: #666;
              margin: 0 0 12px 0;
              line-height: 1.4;
            }

            .status-progress {
              margin-top: 12px;
            }
          }
        }
      }
    }
  }

  .model-option {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .model-name {
      font-weight: 500;
    }

    .model-status {
      font-size: 12px;
      padding: 2px 8px;
      border-radius: 4px;
      background: #f56c6c;
      color: white;

      &.available {
        background: #67c23a;
      }
    }
  }
}

@media (max-width: 768px) {
  .one-click-synthesis {
    .synthesis-content {
      .version-section .version-grid {
        grid-template-columns: 1fr;
      }

      .preview-section .preview-grid {
        grid-template-columns: repeat(2, 1fr);
      }

      .synthesis-control {
        .synthesis-status .status-card {
          flex-direction: column;
          text-align: center;
          gap: 12px;
        }

        .final-result .result-card .result-content .result-actions {
          flex-direction: column;
        }
      }
    }
  }
}
</style>
