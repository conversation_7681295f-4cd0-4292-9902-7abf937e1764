<script setup>
import { ref } from 'vue'
import { VideoCamera, Film, MagicStick, Check } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'version-selected'])

// 版本配置
const versions = [
  {
    key: 'M',
    title: 'M版',
    description: '基础合成 · 简单操作 · 快速生成',
    icon: VideoCamera,
    iconClass: 'm-icon',
    buttonType: 'primary',
    features: ['快速生成', '简单易用', '基础功能']
  },
  {
    key: 'V',
    title: 'V版',
    description: '高级特效 · 互动能力 · 多模型',
    icon: Film,
    iconClass: 's-icon',
    buttonType: 'default',
    badge: '推荐',
    features: ['高级特效', '多模型支持', '互动能力强']
  },
  {
    key: 'H',
    title: 'H版',
    description: '高清画质 · 多语言支持 · AI增强',
    icon: MagicStick,
    iconClass: 'h-icon',
    buttonType: 'default',
    features: ['高清画质', '多语言', 'AI增强']
  }
]

// 路由路径映射
const ROUTE_PATHS = {
  M: '/szbVideo/createMusetalk',
  V: '/szbVideo/synthesisCreate',
  H: '/szbVideo/createHeygem'
}

// 处理版本选择
function handleVersionSelect(version) {
  emit('version-selected', {
    version: version.key,
    path: ROUTE_PATHS[version.key] || ROUTE_PATHS.M
  })
  handleClose()
}

// 关闭对话框
function handleClose() {
  emit('update:visible', false)
}
</script>

<template>
  <el-dialog 
    :model-value="visible"
    @update:model-value="emit('update:visible', $event)"
    title="选择合成版本" 
    width="720px" 
    destroy-on-close 
    center 
    align-center 
    custom-class="version-selector-dialog"
    :close-on-click-modal="false"
  >
    <div class="version-selector">
      <div class="selector-header">
        <h3 class="selector-title">选择您需要的视频合成版本</h3>
        <p class="selector-subtitle">不同版本提供不同的功能特性，请根据需求选择</p>
      </div>

      <div class="version-grid">
        <div 
          v-for="version in versions" 
          :key="version.key"
          class="version-card"
          :class="{ 'recommended': version.badge }"
          @click="handleVersionSelect(version)"
        >
          <!-- 推荐标签 -->
          <div v-if="version.badge" class="version-badge">{{ version.badge }}</div>
          
          <!-- 版本图标 -->
          <div class="version-icon" :class="version.iconClass">
            <el-icon><component :is="version.icon" /></el-icon>
          </div>
          
          <!-- 版本信息 -->
          <div class="version-info">
            <h4 class="version-title">{{ version.title }}</h4>
            <p class="version-description">{{ version.description }}</p>
            
            <!-- 特性列表 -->
            <ul class="feature-list">
              <li v-for="feature in version.features" :key="feature" class="feature-item">
                <el-icon class="feature-icon"><Check /></el-icon>
                <span>{{ feature }}</span>
              </li>
            </ul>
          </div>
          
          <!-- 选择按钮 -->
          <div class="version-action">
            <el-button 
              :type="version.buttonType" 
              class="select-button"
              size="large"
            >
              选择此版本
            </el-button>
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="large">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped>
// 对话框样式
:deep(.version-selector-dialog) {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  
  .el-dialog__header {
    padding: 24px 32px 16px;
    margin: 0;
    border-bottom: 1px solid #f0f0f0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    
    .el-dialog__title {
      font-size: 20px;
      font-weight: 600;
      color: white;
    }
  }
  
  .el-dialog__body {
    padding: 32px;
    background: #fafbfc;
  }
  
  .el-dialog__footer {
    padding: 16px 32px 24px;
    text-align: center;
    background: #fafbfc;
    border-top: 1px solid #f0f0f0;
  }
}

.version-selector {
  .selector-header {
    text-align: center;
    margin-bottom: 32px;
    
    .selector-title {
      font-size: 24px;
      font-weight: 600;
      color: #303133;
      margin: 0 0 8px 0;
    }
    
    .selector-subtitle {
      font-size: 14px;
      color: #909399;
      margin: 0;
    }
  }
  
  .version-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 24px;
    
    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 16px;
    }
  }
  
  .version-card {
    position: relative;
    background: white;
    border-radius: 16px;
    padding: 24px;
    text-align: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid #e4e7ed;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    
    &:hover {
      transform: translateY(-8px);
      box-shadow: 0 16px 40px rgba(0, 0, 0, 0.12);
      border-color: #667eea;
      
      &::before {
        opacity: 1;
      }
      
      .version-icon {
        transform: scale(1.1);
      }
      
      .select-button {
        transform: translateY(-2px);
      }
    }
    
    &.recommended {
      border-color: #67C23A;
      
      &:hover {
        border-color: #67C23A;
        box-shadow: 0 16px 40px rgba(103, 194, 58, 0.2);
      }
    }
    
    .version-badge {
      position: absolute;
      top: -2px;
      right: 16px;
      background: linear-gradient(45deg, #67C23A, #85ce61);
      color: white;
      font-size: 12px;
      font-weight: 600;
      padding: 4px 12px;
      border-radius: 0 0 12px 12px;
      box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
    }
    
    .version-icon {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      margin: 0 auto 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      font-size: 32px;
      position: relative;
      z-index: 1;
      
      &.m-icon {
        background: linear-gradient(135deg, #409EFF, #53a8ff);
        color: white;
        box-shadow: 0 8px 24px rgba(64, 158, 255, 0.3);
      }
      
      &.s-icon {
        background: linear-gradient(135deg, #67C23A, #85ce61);
        color: white;
        box-shadow: 0 8px 24px rgba(103, 194, 58, 0.3);
      }
      
      &.h-icon {
        background: linear-gradient(135deg, #E6A23C, #f0b90b);
        color: white;
        box-shadow: 0 8px 24px rgba(230, 162, 60, 0.3);
      }
    }
    
    .version-info {
      position: relative;
      z-index: 1;
      margin-bottom: 24px;
      
      .version-title {
        font-size: 20px;
        font-weight: 600;
        color: #303133;
        margin: 0 0 8px 0;
      }
      
      .version-description {
        font-size: 13px;
        color: #909399;
        margin: 0 0 16px 0;
        line-height: 1.5;
      }
      
      .feature-list {
        list-style: none;
        padding: 0;
        margin: 0;
        text-align: left;
        
        .feature-item {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 6px;
          font-size: 12px;
          color: #606266;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .feature-icon {
            color: #67C23A;
            font-size: 14px;
          }
        }
      }
    }
    
    .version-action {
      position: relative;
      z-index: 1;
      
      .select-button {
        width: 100%;
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.3s ease;
        
        &.el-button--primary {
          background: linear-gradient(135deg, #409EFF, #53a8ff);
          border: none;
        }
      }
    }
  }
}

.dialog-footer {
  .el-button {
    min-width: 100px;
    border-radius: 8px;
  }
}
</style>
