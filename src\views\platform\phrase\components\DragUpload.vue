<template>
    <div
        class="drag-upload-area"
        :class="{ 'drag-over': isDragOver }"
        @drop="handleFileDrop"
        @dragover="handleDragOver"
        @dragenter="handleDragEnter"
        @dragleave="handleDragLeave"
    >
        <div class="drag-upload-content">
            <el-icon class="upload-icon"><UploadFilled /></el-icon>
            <div class="upload-text">
                <p>拖拽txt文件到此处上传</p>
                <p class="upload-hint">或 <el-button type="primary" link @click="triggerFileInput">点击选择文件</el-button></p>
                <p class="format-hint">文件格式：每行一个词汇和权重，用空格分隔</p>
            </div>
            <div class="upload-actions">
                <el-button
                    type="info"
                    size="small"
                    @click="downloadTestFile"
                    class="download-test-btn"
                    plain
                >
                    <el-icon><Download /></el-icon>
                    下载示例文件
                </el-button>
            </div>
        </div>
        <input
            ref="fileInput"
            type="file"
            accept=".txt"
            style="display: none"
            @change="handleFileSelect"
        />
    </div>
</template>

<script setup name="DragUpload">
import { ref, getCurrentInstance } from 'vue'
import { UploadFilled, Download } from '@element-plus/icons-vue'

const { proxy } = getCurrentInstance()

// Props
const props = defineProps({
    maxSize: {
        type: Number,
        default: 1024 * 1024 // 1MB
    },
    accept: {
        type: String,
        default: '.txt'
    }
})

// Emits
const emit = defineEmits(['file-parsed', 'file-error'])

// 响应式数据
const isDragOver = ref(false)
const fileInput = ref(null)

// 方法
/** 处理拖拽进入 */
function handleDragEnter(e) {
    e.preventDefault()
    e.stopPropagation()
    isDragOver.value = true
}

/** 处理拖拽悬停 */
function handleDragOver(e) {
    e.preventDefault()
    e.stopPropagation()
    isDragOver.value = true
}

/** 处理拖拽离开 */
function handleDragLeave(e) {
    e.preventDefault()
    e.stopPropagation()
    isDragOver.value = false
}

/** 处理文件拖拽放下 */
function handleFileDrop(e) {
    e.preventDefault()
    e.stopPropagation()
    isDragOver.value = false

    const files = e.dataTransfer.files
    if (files.length > 0) {
        const file = files[0]
        processFile(file)
    }
}

/** 触发文件选择 */
function triggerFileInput() {
    fileInput.value.click()
}

/** 处理文件选择 */
function handleFileSelect(e) {
    const files = e.target.files
    if (files.length > 0) {
        const file = files[0]
        processFile(file)
    }
    e.target.value = ''
}

/** 处理文件内容 */
function processFile(file) {
    if (!file.name.toLowerCase().endsWith('.txt')) {
        const error = '请选择txt格式的文件'
        proxy.$modal.msgError(error)
        emit('file-error', error)
        return
    }

    const reader = new FileReader()
    reader.onload = function(e) {
        try {
            const content = e.target.result
            parseFileContent(content)
        } catch (error) {
            console.error('读取文件失败:', error)
            const errorMsg = '读取文件失败，请检查文件格式'
            proxy.$modal.msgError(errorMsg)
            emit('file-error', errorMsg)
        }
    }
    reader.onerror = function() {
        const errorMsg = '读取文件失败'
        proxy.$modal.msgError(errorMsg)
        emit('file-error', errorMsg)
    }
    reader.readAsText(file, 'UTF-8')
}

/** 解析文件内容 */
function parseFileContent(content) {
    const lines = content.split('\n').filter(line => line.trim())

    if (lines.length === 0) {
        const error = '文件内容为空'
        proxy.$modal.msgError(error)
        emit('file-error', error)
        return
    }

    const newWordWeights = []
    const errors = []

    lines.forEach((line, index) => {
        const trimmedLine = line.trim()
        if (!trimmedLine) return

        const parts = trimmedLine.split(/\s+/)

        if (parts.length < 2) {
            errors.push(`第${index + 1}行格式错误：缺少权重值`)
            return
        }

        const word = parts[0].trim()
        const weightStr = parts[1].trim()

        if (!word) {
            errors.push(`第${index + 1}行格式错误：词汇为空`)
            return
        }

        const wordError = validateWordFormat(word)
        if (wordError) {
            errors.push(`第${index + 1}行词汇格式错误：${wordError}`)
            return
        }

        const weight = parseFloat(weightStr)
        if (isNaN(weight)) {
            errors.push(`第${index + 1}行权重格式错误：权重必须是数字`)
            return
        }

        if (weight < -6 || weight > 5) {
            errors.push(`第${index + 1}行权重超出范围：权重必须在-6到5之间`)
            return
        }

        const existingIndex = newWordWeights.findIndex(item => item.word === word)
        if (existingIndex !== -1) {
            newWordWeights[existingIndex].weight = weight
        } else {
            newWordWeights.push({
                word: word,
                weight: weight,
                error: null
            })
        }
    })

    if (errors.length > 0) {
        const errorMsg = errors.slice(0, 5).join('\n') + (errors.length > 5 ? `\n...还有${errors.length - 5}个错误` : '')
        proxy.$modal.msgError(`文件解析失败：\n${errorMsg}`)
        emit('file-error', errorMsg)
        return
    }

    if (newWordWeights.length === 0) {
        const error = '文件中没有有效的词汇权重数据'
        proxy.$modal.msgError(error)
        emit('file-error', error)
        return
    }

    // 检查词汇数量限制
    if (newWordWeights.length > 300) {
        const error = `文件包含${newWordWeights.length}个词汇，超过了300个词汇的限制`
        proxy.$modal.msgError(error)
        emit('file-error', error)
        return
    }

    // 发送解析成功的数据
    emit('file-parsed', newWordWeights)
    proxy.$modal.msgSuccess(`成功解析${newWordWeights.length}个词汇`)
}

/** 验证词汇格式 */
function validateWordFormat(word) {
    if (word.length > 10) {
        return '词汇长度不能超过10个字符'
    }

    const numberRegex = /[0-9]/
    if (numberRegex.test(word)) {
        return '词汇不能包含数字，如"58.9元"需要替换为"五十八点九元"'
    }

    const punctuationRegex = /[^\u4e00-\u9fa5a-zA-Z]/
    if (punctuationRegex.test(word)) {
        return '词汇不能包含标点符号和特殊字符'
    }

    return null
}

/** 下载测试文件 */
function downloadTestFile() {
    // 创建测试文件内容
    const testContent = `肖申克的救赎 3
霸王别姬 2
这个杀手不太冷 4
阿甘正传 -5
美丽人生 -1
楚门的世界 1`

    // 创建 Blob 对象
    const blob = new Blob([testContent], { type: 'text/plain;charset=utf-8' })

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = '词汇示例.txt'

    // 触发下载
    document.body.appendChild(link)
    link.click()

    // 清理
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    proxy.$modal.msgSuccess('示例文件下载成功')
}
</script>

<style scoped>
@import './styles/upload.scss';
</style>
