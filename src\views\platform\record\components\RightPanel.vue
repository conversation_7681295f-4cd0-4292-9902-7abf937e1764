<template>
  <div class="right-panel">
    <!-- 标签页导航 -->
    <el-tabs v-model="activeTab" class="right-tabs">
      <!-- 章节速览 -->
      <el-tab-pane label="章节速览" name="chapters">
        <ChapterOverview :overview="record?.overview" />
      </el-tab-pane>

      <!-- 发言总结 -->
      <el-tab-pane label="发言总结" name="speakers">
        <SpeakersSummary :statements="record?.statements" />
      </el-tab-pane>

      <!-- 问答回顾 -->
      <el-tab-pane label="问答回顾" name="qa">
        <QAReview :qa-review="record?.qaReview" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import ChapterOverview from './ChapterOverview.vue'
import SpeakersSummary from './SpeakersSummary.vue'
import QAReview from './QAReview.vue'

const props = defineProps({
  record: {
    type: Object,
    default: () => ({})
  }
})

const activeTab = ref('chapters') // 当前活动的标签页
</script>

<style lang="scss" scoped>
.right-panel {
  flex: 0 0 40%;
  max-width: 40%;
  padding: 32px;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  overflow-y: auto;
  height: 100%;
  margin-top: 40px;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.3);
    border-radius: 3px;
    transition: background 0.2s ease;

    &:hover {
      background: rgba(59, 130, 246, 0.5);
    }
  }
}

/* 右侧标签页样式 */
.right-tabs {
  margin-top: 0;
  padding-top: 0;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 24px;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.08),
    0 2px 8px rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.9);
}

/* 隐藏默认的标签页样式 */
.right-tabs :deep(.el-tabs__header) {
  margin-bottom: 24px;
  margin-top: 0;
  background: none;
  border: none;
  padding: 0;
}

.right-tabs :deep(.el-tabs__nav-wrap) {
  padding: 0;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16px;
  padding: 6px;
  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.right-tabs :deep(.el-tabs__nav-scroll) {
  display: flex;
  justify-content: space-between;
}

.right-tabs :deep(.el-tabs__nav) {
  border: none;
  display: flex;
  width: 100%;
  gap: 4px;
}

.right-tabs :deep(.el-tabs__active-bar) {
  display: none !important;
}

.right-tabs :deep(.el-tabs__item) {
  font-size: 13px;
  font-weight: 600;
  padding: 12px 16px;
  border-radius: 12px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  color: #64748b;
  background: transparent;
  border: none;
  position: relative;
  flex: 1;
  text-align: center;
  margin: 0;
  height: auto;
  line-height: 1.4;
}

.right-tabs :deep(.el-tabs__item::before) {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 12px;
  opacity: 0;
  transition: all 0.4s ease;
  z-index: -1;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.right-tabs :deep(.el-tabs__item.is-active) {
  color: #1e293b;
  font-weight: 700;
  transform: translateY(-1px);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.right-tabs :deep(.el-tabs__item.is-active::before) {
  opacity: 1;
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.12),
    0 2px 8px rgba(0, 0, 0, 0.08);
}

.right-tabs :deep(.el-tabs__item:hover:not(.is-active)) {
  color: #475569;
  transform: translateY(-0.5px);
}

.right-tabs :deep(.el-tabs__item:hover:not(.is-active)::before) {
  opacity: 0.6;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 为每个标签页添加图标 */
.right-tabs :deep(.el-tabs__item:nth-child(1)::after) {
  content: '📖';
  margin-left: 6px;
  font-size: 12px;
}

.right-tabs :deep(.el-tabs__item:nth-child(2)::after) {
  content: '💬';
  margin-left: 6px;
  font-size: 12px;
}

.right-tabs :deep(.el-tabs__item:nth-child(3)::after) {
  content: '❓';
  margin-left: 6px;
  font-size: 12px;
}

.right-tabs :deep(.el-tabs__content) {
  padding: 0;
}
</style>
