<template>
    <div v-if="visible" class="advanced-filter-section">
        <div class="filter-content">
            <div class="filter-item">
                <label class="filter-label">音视频名称：</label>
                <el-input
                    v-model="filterParams.vaName"
                    placeholder="请输入音视频名称"
                    size="default"
                    class="filter-input"
                    clearable
                    @change="handleFilterChange"
                />
            </div>
            <div class="filter-item">
                <label class="filter-label">开始时间：</label>
                <el-date-picker
                    v-model="filterParams.startTime"
                    type="datetime"
                    placeholder="选择开始时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    size="default"
                    class="filter-date-picker"
                    @change="handleFilterChange"
                />
            </div>
            <div class="filter-item">
                <label class="filter-label">结束时间：</label>
                <el-date-picker
                    v-model="filterParams.endTime"
                    type="datetime"
                    placeholder="选择结束时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    size="default"
                    class="filter-date-picker"
                    @change="handleFilterChange"
                />
            </div>
            <div class="filter-actions">
                <el-button
                    type="primary"
                    size="small"
                    @click="handleSearch"
                    class="search-btn"
                >
                    查询
                </el-button>
                <el-button
                    type="danger"
                    size="small"
                    plain
                    @click="clearFilters"
                    class="clear-filter-btn"
                >
                    清除筛选条件
                </el-button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    modelValue: {
        type: Object,
        default: () => ({
            vaName: null,
            startTime: null,
            endTime: null
        })
    }
})

const emit = defineEmits(['update:modelValue', 'search', 'clear', 'filter-change'])

const filterParams = ref({ ...props.modelValue })

// 监听外部传入的值变化
watch(() => props.modelValue, (newVal) => {
    filterParams.value = { ...newVal }
}, { deep: true })

// 监听内部值变化，同步到外部
watch(filterParams, (newVal) => {
    emit('update:modelValue', { ...newVal })
}, { deep: true })

const handleFilterChange = () => {
    emit('filter-change')
}

const handleSearch = () => {
    emit('search')
}

const clearFilters = () => {
    filterParams.value = {
        vaName: null,
        startTime: null,
        endTime: null
    }
    emit('clear')
}
</script>

<style lang="scss" scoped>
.advanced-filter-section {
    background: #ffffff;
    border-radius: 12px;
    padding: 20px 24px;
    margin: 0 32px 24px 32px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e4e7ed;
    transition: all 0.3s ease;

    .filter-content {
        display: flex;
        gap: 24px;
        align-items: center;
        flex-wrap: wrap;
        justify-content: space-between;

        .filter-item {
            display: flex;
            align-items: center;
            gap: 8px;

            .filter-label {
                font-size: 14px;
                color: #606266;
                font-weight: 500;
                white-space: nowrap;
            }

            .filter-input {
                width: 200px;

                :deep(.el-input__wrapper) {
                    border-radius: 8px;
                    transition: all 0.2s ease;

                    &:hover {
                        box-shadow: 0 0 0 1px #c6e2ff;
                    }

                    &.is-focus {
                        box-shadow: 0 0 0 1px #409eff;
                    }
                }
            }

            .filter-date-picker {
                width: 200px;

                :deep(.el-input__wrapper) {
                    border-radius: 8px;
                    transition: all 0.2s ease;

                    &:hover {
                        box-shadow: 0 0 0 1px #c6e2ff;
                    }

                    &.is-focus {
                        box-shadow: 0 0 0 1px #409eff;
                    }
                }
            }
        }

        .filter-actions {
            margin-left: auto;
            display: flex;
            gap: 12px;

            .search-btn {
                border-radius: 8px;
                font-weight: 500;
                transition: all 0.3s ease;

                &:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
                }
            }

            .clear-filter-btn {
                border-radius: 8px;
                font-weight: 500;
                transition: all 0.3s ease;

                &:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
                }
            }
        }
    }

    // 添加动画效果
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
