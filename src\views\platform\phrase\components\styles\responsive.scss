/* 响应式优化 */
@media (max-width: 1200px) {
    .phrase-dialog :deep(.el-dialog) {
        width: 90% !important;
        max-width: 900px;
    }

    .dialog-content {
        height: 500px;
    }

    .left-form-section {
        flex: 0 0 350px;
    }
}

@media (max-width: 1024px) {
    .phrase-dialog :deep(.el-dialog) {
        width: 95% !important;
        max-width: 800px;
    }

    .dialog-content {
        height: 450px;
    }

    .left-form-section {
        flex: 0 0 320px;
    }

    .manual-add-section {
        margin-top: 16px;
    }

    .input-row {
        flex-direction: column;
        gap: 8px;
    }

    .weight-input {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .dialog-content {
        flex-direction: column;
        height: auto;
        max-height: 70vh;
        overflow-y: auto;
    }

    .left-form-section {
        flex: none;
        padding-right: 0;
        margin-bottom: 20px;
    }

    .left-form-section::after {
        display: none;
    }

    .right-words-section {
        flex: none;
        height: 300px;
        padding-left: 0;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.word-item {
    animation: fadeInUp 0.3s ease forwards;
}

.word-item:nth-child(even) {
    animation-delay: 0.1s;
}

.word-item:nth-child(odd) {
    animation-delay: 0.05s;
}
