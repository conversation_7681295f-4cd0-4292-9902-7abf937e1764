<template>
    <div class="record-footer">
        <pagination 
            v-show="total > 0" 
            :total="total" 
            v-model:page="currentPage"
            v-model:limit="pageSize" 
            @pagination="handlePagination" 
        />
    </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
    total: {
        type: Number,
        default: 0
    },
    page: {
        type: Number,
        default: 1
    },
    limit: {
        type: Number,
        default: 10
    }
})

const emit = defineEmits(['update:page', 'update:limit', 'pagination'])

const currentPage = ref(props.page)
const pageSize = ref(props.limit)

// 监听外部传入的值变化
watch(() => props.page, (newVal) => {
    if (currentPage.value !== newVal) {
        currentPage.value = newVal
    }
})

watch(() => props.limit, (newVal) => {
    if (pageSize.value !== newVal) {
        pageSize.value = newVal
    }
})

// 监听内部值变化，同步到外部并触发分页事件
watch(currentPage, (newVal, oldVal) => {
    if (newVal !== oldVal && newVal !== props.page) {
        emit('update:page', newVal)
        // 延迟触发分页事件，确保父组件的v-model已更新
        setTimeout(() => {
            handlePagination()
        }, 0)
    }
})

watch(pageSize, (newVal, oldVal) => {
    if (newVal !== oldVal && newVal !== props.limit) {
        emit('update:limit', newVal)
        // 页面大小变化时重置到第一页并触发分页事件
        if (currentPage.value !== 1) {
            currentPage.value = 1
            emit('update:page', 1)
        }
        // 延迟触发分页事件，确保父组件的v-model已更新
        setTimeout(() => {
            handlePagination()
        }, 0)
    }
})

const handlePagination = () => {
    const paginationData = { page: currentPage.value, limit: pageSize.value }
    console.log('分页事件触发:', paginationData)
    emit('pagination', paginationData)
}
</script>

<style lang="scss" scoped>
.record-footer {
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    padding: 24px 0 0 32px;
    font-size: 15px;
    color: #7a8599;

    .record-pagination {
        margin: 0 12px;
    }

    .record-page-size {
        margin-left: 8px;
    }
}
</style>
