<template>
  <div class="dialogue-step">
    <div class="panel-header">
      <div class="header-info">
        <h3 class="panel-title">
          <el-icon class="panel-icon"><Edit /></el-icon>
          编写对话内容
        </h3>
        <p class="panel-desc">设计数字人之间的对话内容，创造自然的对话流程</p>
      </div>
      <div class="header-actions">
        <el-button type="primary" @click="addDialogue" class="add-btn">
          <el-icon><Plus /></el-icon>添加对话
        </el-button>
        <el-button @click="clearDialogue" class="clear-btn">
          清空对话
        </el-button>
      </div>
    </div>

    <div class="dialogue-content">
      <div class="dialogue-stats">
        <span class="stats-text">共 {{ internalDialogueContent.length }} 条对话</span>
      </div>

      <div v-if="internalDialogueContent.length === 0" class="empty-state">
        <div class="empty-icon">
          <el-icon><Edit /></el-icon>
        </div>
        <h4 class="empty-title">暂无对话内容</h4>
        <p class="empty-desc">请添加对话内容来创建数字人对话</p>
        <el-button type="primary" @click="addDialogue" class="empty-action">
          添加第一条对话
        </el-button>
      </div>

      <div v-else class="dialogue-list">
        <div v-for="(dialogue, index) in internalDialogueContent" :key="dialogue.id" class="dialogue-item" :style="{ animationDelay: `${index * 0.1}s` }">
          <div class="dialogue-header">
            <div class="dialogue-number">{{ index + 1 }}</div>
            <el-select 
              v-model="dialogue.speaker" 
              placeholder="选择发言人" 
              @change="updateSpeakerName(dialogue)"
              class="speaker-select"
            >
              <el-option 
                v-for="human in digitalHumans" 
                :key="human.id" 
                :label="human.name" 
                :value="human.id" 
              />
            </el-select>
            <el-button 
              type="danger" 
              size="small" 
              @click="removeDialogue(index)" 
              :disabled="internalDialogueContent.length <= 1"
              class="remove-btn"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
          <div class="dialogue-body">
            <el-input 
              v-model="dialogue.text" 
              type="textarea" 
              :rows="3" 
              placeholder="请输入对话内容..." 
              maxlength="500" 
              show-word-limit 
              class="dialogue-input"
              @input="updateDialogueText(dialogue.id, dialogue.text)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { Edit, Plus, Delete } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const props = defineProps({
  digitalHumans: {
    type: Array,
    default: () => []
  },
  dialogueContent: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update-dialogue'])

// 组件内部状态
const internalDialogueContent = ref([...props.dialogueContent])

// 直接使用传入的数字人列表
const digitalHumans = computed(() => {
  return props.digitalHumans
})

// 初始化对话内容 - 智能创建默认对话
const initializeDialogueContent = () => {
  if (internalDialogueContent.value.length === 0 && digitalHumans.value.length >= 2) {
    // 为每个数字人创建一条默认对话
    digitalHumans.value.forEach((human, index) => {
      const dialogue = {
        id: `dialogue_${Date.now()}_${index}`,
        speaker: human.id,
        speakerName: human.name,
        text: `这是${human.name}的对话内容，请修改为实际的对话文本。`
      }
      internalDialogueContent.value.push(dialogue)
    })
    emit('update-dialogue', internalDialogueContent.value)
    //ElMessage.success(`已为 ${digitalHumans.value.length} 个数字人创建默认对话`)
  } else if (internalDialogueContent.value.length === 0) {
    // 如果没有数字人，创建一个空对话
    addDialogue()
  }
}

const addDialogue = () => {
  const newDialogue = {
    id: `dialogue_${Date.now()}`,
    speaker: '',
    speakerName: '',
    text: ''
  }
  internalDialogueContent.value.push(newDialogue)
  emit('update-dialogue', internalDialogueContent.value)
}

const removeDialogue = (index) => {
  if (internalDialogueContent.value.length > 1) {
    internalDialogueContent.value.splice(index, 1)
    emit('update-dialogue', internalDialogueContent.value)
  } else {
    ElMessage.warning('至少需要保留一条对话')
  }
}

const clearDialogue = () => {
  ElMessageBox.confirm(
    '确定要清空所有对话内容吗？',
    '确认清空',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    internalDialogueContent.value = []
    initializeDialogueContent()
    ElMessage.success('已清空对话内容')
  }).catch(() => {})
}

const updateSpeakerName = (dialogue) => {
  const human = digitalHumans.value.find(h => h.id === dialogue.speaker)
  if (human) {
    dialogue.speakerName = human.name
    emit('update-dialogue', internalDialogueContent.value)
  }
}

const updateDialogueText = (dialogueId, text) => {
  const dialogue = internalDialogueContent.value.find(d => d.id === dialogueId)
  if (dialogue) {
    dialogue.text = text
    emit('update-dialogue', internalDialogueContent.value)
  }
}

// 监听数字人变化，自动初始化对话
watch(() => digitalHumans.value.length, (newLength, oldLength) => {
  if (newLength >= 2 && oldLength < 2) {
    initializeDialogueContent()
  }
}, { immediate: true })

watch(() => props.dialogueContent, (newContent) => {
  internalDialogueContent.value = [...newContent]
}, { immediate: true })

onMounted(() => {
  if (digitalHumans.value.length >= 2 && internalDialogueContent.value.length === 0) {
    initializeDialogueContent()
  }
})
</script>

<style lang="scss" scoped>
.dialogue-step {
  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 30px;
    padding: 24px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 16px;
    border: 1px solid #e9ecef;

    .header-info {
      .panel-title {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 24px;
        font-weight: 700;
        color: #333;
        margin: 0 0 8px 0;

        .panel-icon {
          font-size: 28px;
          color: #667eea;
        }
      }

      .panel-desc {
        font-size: 16px;
        color: #666;
        margin: 0;
        line-height: 1.5;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;

      .add-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 600;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        }
      }

      .clear-btn {
        background: white;
        border: 2px solid #e9ecef;
        color: #666;
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 600;
        transition: all 0.3s ease;

        &:hover {
          border-color: #f56c6c;
          color: #f56c6c;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(245, 108, 108, 0.2);
        }
      }
    }
  }

  .dialogue-stats {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    border: 1px solid #e9ecef;

    .stats-text {
      font-weight: 600;
      color: #333;
      font-size: 16px;
    }
  }

  .empty-state {
    text-align: center;
    padding: 60px 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 16px;
    border: 2px dashed #dee2e6;

    .empty-icon {
      font-size: 64px;
      color: #dee2e6;
      margin-bottom: 20px;
    }

    .empty-title {
      font-size: 20px;
      font-weight: 600;
      color: #666;
      margin: 0 0 12px 0;
    }

    .empty-desc {
      font-size: 16px;
      color: #999;
      margin: 0 0 24px 0;
      line-height: 1.5;
    }

    .empty-action {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      border-radius: 12px;
      padding: 12px 32px;
      font-weight: 600;
      font-size: 16px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
      }
    }
  }

  .dialogue-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .dialogue-item {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    animation: slideInUp 0.6s ease-out;
    animation-fill-mode: both;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
    }

    .dialogue-header {
      display: flex;
      align-items: center;
      gap: 16px;
      padding: 16px 20px;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-bottom: 1px solid #e9ecef;

      .dialogue-number {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 14px;
        flex-shrink: 0;
      }

      .speaker-select {
        flex: 1;
        max-width: 200px;
      }

      .remove-btn {
        background: rgba(245, 108, 108, 0.1);
        border: 1px solid rgba(245, 108, 108, 0.3);
        color: #f56c6c;
        transition: all 0.3s ease;

        &:hover:not(:disabled) {
          background: rgba(245, 108, 108, 0.2);
          border-color: rgba(245, 108, 108, 0.5);
          transform: scale(1.05);
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
    }

    .dialogue-body {
      padding: 20px;

      .dialogue-input {
        :deep(.el-textarea__inner) {
          border-radius: 12px;
          border: 2px solid #e9ecef;
          transition: all 0.3s ease;
          font-size: 16px;
          line-height: 1.6;

          &:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
          }
        }
      }
    }
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .dialogue-step {
    .panel-header {
      flex-direction: column;
      gap: 20px;
      text-align: center;

      .header-actions {
        width: 100%;
        justify-content: center;

        .add-btn,
        .clear-btn {
          flex: 1;
        }
      }
    }

    .dialogue-item .dialogue-header {
      flex-wrap: wrap;
      gap: 12px;

      .speaker-select {
        max-width: none;
        width: 100%;
      }
    }
  }
}
</style>
