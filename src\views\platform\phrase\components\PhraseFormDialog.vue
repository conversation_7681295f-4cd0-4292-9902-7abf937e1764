<template>
    <el-dialog :title="title" v-model="visible" width="1000px" append-to-body class="phrase-dialog"
        @close="handleClose">
        <div class="dialog-content">
            <!-- 左侧表单区域 -->
            <div class="left-form-section">
                <el-form ref="phraseRef" :model="form" :rules="rules" label-width="80px">
                    <el-form-item label="词表表名" prop="name">
                        <el-input v-model="form.name" placeholder="请输入词表表名" />
                    </el-form-item>
                    <el-form-item label="词表描述" prop="description">
                        <el-input v-model="form.description" placeholder="请输入词表描述" type="textarea" :rows="3" />
                    </el-form-item>
                    <el-form-item prop="wordWeights">
                        <template #label>
                            <div class="form-label-with-tip">
                                <span class="label-text">词表权重</span>
                                <div class="help-icon-wrapper">
                                    <el-tooltip content="权重 > 0：增大识别概率，常用值为2<br />
                                        权重 = -6：尽量不识别该词语<br />
                                        权重过大可能影响其他词语识别准确性<br />
                                        权重取值范围为 [-6,5]<br />
                                        需将词汇中的数字转换为汉字<br />
                                        词汇不能包含标点符号和特殊字符<br />
                                        词汇长度不能超过10个字符
                                        " placement="top" effect="dark" raw-content>
                                        <el-icon class="help-icon">
                                            <QuestionFilled />
                                        </el-icon>
                                    </el-tooltip>
                                </div>
                            </div>
                        </template>
                        <!-- 拖拽上传组件 -->
                        <DragUpload @file-parsed="handleFileParsed" @file-error="handleFileError" />
                    </el-form-item>
                    <el-form-item>
                        <template #label>
                            <span class="manual-add-label">手动添加</span>
                        </template>
                        <div class="manual-add-controls">
                            <el-input v-model="newWord" placeholder="请输入词汇" class="word-input" size="small" clearable
                                @keyup.enter="addSingleWordWeight" />
                            <el-input-number v-model="newWeight" :min="-6" :max="5" :step="1" placeholder="权重"
                                class="weight-input" size="small" controls-position="right" />
                            <el-button type="primary" class="add-button" size="small" @click="addSingleWordWeight"
                                :disabled="!newWord || !newWord.trim() || wordWeightsList.filter(item => item.word && item.word.trim()).length >= 300">
                                <el-icon>
                                    <Plus />
                                </el-icon>
                                添加
                            </el-button>
                        </div>
                        <div v-if="wordWeightsList.filter(item => item.word && item.word.trim()).length >= 280"
                            class="word-limit-hint">
                            <el-icon>
                                <WarningFilled />
                            </el-icon>
                            <span v-if="wordWeightsList.filter(item => item.word && item.word.trim()).length >= 300">
                                已达到300个词汇上限
                            </span>
                            <span v-else>
                                还可添加 {{300 - wordWeightsList.filter(item => item.word && item.word.trim()).length}}
                                个词汇
                            </span>
                        </div>
                    </el-form-item>
                </el-form>
            </div>

            <!-- 右侧词汇列表区域 -->
            <div class="right-words-section">
                <div class="words-header">
                    <span class="words-title">
                        词汇列表 ({{wordWeightsList.filter(item => item.word && item.word.trim()).length}}/300)
                        <span v-if="wordWeightsList.filter(item => item.word && item.word.trim()).length >= 300"
                            class="limit-warning">已达上限</span>
                    </span>
                    <el-button type="danger" size="small" @click="clearAllWords"
                        v-if="wordWeightsList.length > 1 || (wordWeightsList.length === 1 && wordWeightsList[0].word)">
                        清空全部
                    </el-button>
                </div>
                <div class="words-list-container">
                    <div v-if="!wordWeightsList.some(item => item.word && item.word.trim())" class="empty-words">
                        暂无词汇，请通过左侧上传文件或手动添加
                    </div>
                    <div v-else class="words-list">
                        <div v-for="(item, index) in wordWeightsList" :key="index"
                            v-show="item.word && item.word.trim()" class="word-item">
                            <div class="word-content">
                                <span class="word-text">{{ item.word }}</span>
                                <el-tag :type="getTagType(item.weight)" size="small" class="weight-tag">
                                    权重: {{ item.weight }}
                                </el-tag>
                            </div>
                            <div class="word-actions">
                                <el-button type="primary" link size="small" @click="editWord(index)">
                                    编辑
                                </el-button>
                                <el-button type="danger" link size="small" @click="removeWordWeight(index)">
                                    删除
                                </el-button>
                            </div>
                            <div v-if="item.error" class="error-message">{{ item.error }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" @click="submitForm">确 定</el-button>
                <el-button @click="handleCancel">取 消</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup name="PhraseFormDialog">
import { ref, watch, computed, getCurrentInstance } from 'vue'
import { QuestionFilled, Plus, WarningFilled } from '@element-plus/icons-vue'
import DragUpload from './DragUpload.vue'

const { proxy } = getCurrentInstance()

// Props
const props = defineProps({
    visible: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: ''
    },
    formData: {
        type: Object,
        default: () => ({})
    },
    rules: {
        type: Object,
        default: () => ({})
    }
})

// Emits
const emit = defineEmits(['update:visible', 'submit', 'cancel'])

// 响应式数据
const form = ref({})
const wordWeightsList = ref([{ word: '', weight: 0, error: null }])

// 手动添加词汇相关
const newWord = ref('')
const newWeight = ref(0)

// 计算属性
const visible = computed({
    get: () => props.visible,
    set: (value) => emit('update:visible', value)
})

// 监听 formData 变化
watch(() => props.formData, (newData) => {
    form.value = { ...newData }
    // 处理 wordWeights
    if (newData.wordWeights) {
        try {
            let weightsObj = newData.wordWeights
            if (typeof weightsObj === 'string') {
                weightsObj = JSON.parse(weightsObj)
            }
            wordWeightsList.value = Object.entries(weightsObj).map(([word, weight]) => ({
                word,
                weight: Number(weight),
                error: null
            }))
        } catch (e) {
            console.error('解析词表权重失败:', e)
            wordWeightsList.value = [{ word: '', weight: 0, error: null }]
        }
    } else {
        wordWeightsList.value = [{ word: '', weight: 0, error: null }]
    }
}, { immediate: true, deep: true })

// 监听 wordWeightsList 变化，更新 form.wordWeights
watch(wordWeightsList, () => {
    updateWordWeights()
}, { deep: true })

// 方法
function handleClose() {
    emit('update:visible', false)
}

function handleCancel() {
    emit('cancel')
}

function submitForm() {
    // 验证所有词汇
    let hasWordError = false
    const validWords = wordWeightsList.value.filter(item => item.word && item.word.trim())

    if (validWords.length === 0) {
        proxy.$modal.msgError("请至少添加一个词汇")
        return
    }

    // 检查词汇数量限制
    if (validWords.length > 300) {
        proxy.$modal.msgError(`当前有${validWords.length}个词汇，超过了300个词汇的限制，请删除部分词汇后再提交`)
        return
    }

    wordWeightsList.value.forEach(item => {
        if (item.word && item.word.trim()) {
            validateWord(item)
            if (item.error) {
                hasWordError = true
            }
        }
    })

    if (hasWordError) {
        proxy.$modal.msgError("请修正词汇输入错误后再提交")
        return
    }

    proxy.$refs["phraseRef"].validate(valid => {
        if (valid) {
            emit('submit', form.value)
        }
    })
}

/** 更新词汇权重JSON字符串 */
function updateWordWeights() {
    const weightsObj = {}

    wordWeightsList.value.forEach(item => {
        if (item.word && item.word.trim() && !item.error) {
            weightsObj[item.word.trim()] = item.weight !== undefined ? item.weight : 0
        }
    })

    form.value.wordWeights = JSON.stringify(weightsObj)
}

/** 根据权重值获取标签类型 */
function getTagType(weight) {
    if (weight > 0) {
        return 'success'
    } else if (weight === -6) {
        return 'danger'
    } else if (weight < 0) {
        return 'warning'
    } else {
        return ''
    }
}

/** 添加单个词汇权重 */
function addSingleWordWeight() {
    if (!newWord.value || !newWord.value.trim()) {
        proxy.$modal.msgError('请输入词汇')
        return
    }

    const word = newWord.value.trim()
    const error = validateWordFormat(word)
    if (error) {
        proxy.$modal.msgError(error)
        return
    }

    const existingIndex = wordWeightsList.value.findIndex(item => item.word === word)
    if (existingIndex !== -1) {
        wordWeightsList.value[existingIndex].weight = newWeight.value
        proxy.$modal.msgSuccess('词汇权重已更新')
    } else {
        // 检查词汇数量限制
        const currentValidWords = wordWeightsList.value.filter(item => item.word && item.word.trim()).length
        if (currentValidWords >= 300) {
            proxy.$modal.msgError('单个词汇表最多只能添加300个词汇')
            return
        }

        if (wordWeightsList.value.length === 1 && !wordWeightsList.value[0].word) {
            wordWeightsList.value = []
        }

        wordWeightsList.value.push({
            word: word,
            weight: newWeight.value,
            error: null
        })
        proxy.$modal.msgSuccess('词汇添加成功')
    }

    newWord.value = ''
    newWeight.value = 0
}

/** 编辑词汇 */
function editWord(index) {
    const item = wordWeightsList.value[index]
    newWord.value = item.word
    newWeight.value = item.weight
}

/** 删除词汇权重项 */
function removeWordWeight(index) {
    if (wordWeightsList.value.length === 1) {
        const firstItem = wordWeightsList.value[0]
        if (!firstItem.word || firstItem.word.trim() === '') {
            return
        }
    }

    wordWeightsList.value.splice(index, 1)

    if (wordWeightsList.value.length === 0) {
        wordWeightsList.value.push({ word: '', weight: 0, error: null })
    }
}

/** 清空所有词汇 */
function clearAllWords() {
    proxy.$modal.confirm('确认清空所有词汇吗？').then(() => {
        wordWeightsList.value = [{ word: '', weight: 0, error: null }]
        proxy.$modal.msgSuccess('已清空所有词汇')
    }).catch(() => { })
}

/** 验证词汇格式 */
function validateWordFormat(word) {
    if (word.length > 10) {
        return '词汇长度不能超过10个字符'
    }

    const numberRegex = /[0-9]/
    if (numberRegex.test(word)) {
        return '词汇不能包含数字，如"58.9元"需要替换为"五十八点九元"'
    }

    const punctuationRegex = /[^\u4e00-\u9fa5a-zA-Z]/
    if (punctuationRegex.test(word)) {
        return '词汇不能包含标点符号和特殊字符'
    }

    return null
}

/** 验证词汇 */
function validateWord(item) {
    if (item.word && item.word.trim()) {
        const error = validateWordFormat(item.word.trim())
        item.error = error
    } else {
        item.error = null
    }
}

// ========== 文件上传处理 ==========

/** 处理文件解析成功 */
function handleFileParsed(newWordWeights) {
    // 检查词汇数量限制
    if (newWordWeights.length > 300) {
        proxy.$modal.msgError(`文件包含${newWordWeights.length}个词汇，超过了300个词汇的限制，请减少词汇数量后重新上传`)
        return
    }

    const hasExistingData = wordWeightsList.value.some(item => item.word && item.word.trim())

    if (hasExistingData) {
        appendWordWeights(newWordWeights)
    } else {
        wordWeightsList.value = newWordWeights
    }
}

/** 处理文件解析错误 */
function handleFileError(error) {
    console.error('文件解析错误:', error)
}

/** 追加词汇权重数据 */
function appendWordWeights(newWordWeights) {
    if (wordWeightsList.value.length === 1 && !wordWeightsList.value[0].word) {
        wordWeightsList.value = []
    }

    const currentValidWords = wordWeightsList.value.filter(item => item.word && item.word.trim()).length

    let addedCount = 0
    let updatedCount = 0
    let skippedCount = 0

    newWordWeights.forEach(newItem => {
        const existingIndex = wordWeightsList.value.findIndex(item => item.word === newItem.word)
        if (existingIndex !== -1) {
            // 更新现有词汇
            wordWeightsList.value[existingIndex].weight = newItem.weight
            updatedCount++
        } else {
            // 检查是否超过限制
            if (currentValidWords + addedCount >= 300) {
                skippedCount++
                return
            }
            // 添加新词汇
            wordWeightsList.value.push(newItem)
            addedCount++
        }
    })

    let message = ''
    if (addedCount > 0 && updatedCount > 0) {
        message = `成功添加${addedCount}个新词汇，更新${updatedCount}个已有词汇`
    } else if (addedCount > 0) {
        message = `成功添加${addedCount}个词汇`
    } else if (updatedCount > 0) {
        message = `成功更新${updatedCount}个词汇`
    }

    if (skippedCount > 0) {
        message += `，跳过${skippedCount}个词汇（超过300个限制）`
    }

    proxy.$modal.msgSuccess(message)
}
</script>

<style scoped>
@import './styles/dialog.scss';
@import './styles/form.scss';
@import './styles/words.scss';
@import './styles/responsive.scss';
</style>
