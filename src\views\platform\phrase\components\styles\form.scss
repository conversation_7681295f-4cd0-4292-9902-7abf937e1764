/* 左侧表单区域 */
.left-form-section {
    flex: 0 0 420px;
    padding-right: 24px;
    position: relative;
}

.left-form-section::after {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 1px;
    background: linear-gradient(to bottom, transparent, #e9ecef 20%, #e9ecef 80%, transparent);
}

/* 表单样式美化 */
.left-form-section :deep(.el-form-item) {
    margin-bottom: 16px;
}

/* 减少词表权重相关表单项的间距 */
.left-form-section :deep(.el-form-item:nth-child(3)) {
    margin-bottom: 8px;
}

.left-form-section :deep(.el-form-item:nth-child(4)) {
    margin-bottom: 12px;
}

.left-form-section :deep(.el-form-item__label) {
    font-weight: 600;
    color: #2d3748;
    font-size: 14px;
    margin-bottom: 8px;
}

.left-form-section :deep(.el-input__wrapper) {
    border-radius: 10px;
    border: 2px solid #e2e8f0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: #fafbfc;
}

.left-form-section :deep(.el-input__wrapper:hover) {
    border-color: #cbd5e0;
    background: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

.left-form-section :deep(.el-input__wrapper.is-focus) {
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 4px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-1px);
}

.left-form-section :deep(.el-textarea__inner) {
    border-radius: 10px;
    border: 2px solid #e2e8f0;
    background: #fafbfc;
    resize: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.left-form-section :deep(.el-textarea__inner:hover) {
    border-color: #cbd5e0;
    background: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.left-form-section :deep(.el-textarea__inner:focus) {
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* 表单标签样式 */
.form-label-with-tip {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
    white-space: nowrap;
    min-width: max-content; /* 更好的兼容性替代方案 */
}

.label-text {
    font-size: 14px;
    color: #2d3748;
    font-weight: 600;
    white-space: nowrap;
}

.help-icon-wrapper {
    display: flex;
    justify-content: flex-start;
}

.help-icon {
    font-size: 16px;
    color: #667eea;
    cursor: help;
    transition: all 0.3s ease;
    border-radius: 50%;
    padding: 4px;
    background: rgba(102, 126, 234, 0.1);
}

.help-icon:hover {
    color: #5a6fd8;
    background: rgba(102, 126, 234, 0.2);
    transform: scale(1.1);
}

/* 手动添加标签样式 */
.manual-add-label {
    font-weight: 600;
    color: #2d3748;
    font-size: 14px;
}

/* 手动添加控件样式 */
.manual-add-controls {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 0;
    background: transparent;
    border: none;
    border-radius: 0;
    box-shadow: none;
}

.manual-add-controls .word-input {
    flex: 1;
}

.manual-add-controls .word-input :deep(.el-input__wrapper) {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 36px;
}

.manual-add-controls .word-input :deep(.el-input__wrapper:hover) {
    border-color: #9ca3af;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.manual-add-controls .word-input :deep(.el-input__wrapper.is-focus) {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.manual-add-controls .weight-input {
    width: 100px;
}

.manual-add-controls .weight-input :deep(.el-input__wrapper) {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 36px;
}

.manual-add-controls .weight-input :deep(.el-input__wrapper:hover) {
    border-color: #9ca3af;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.manual-add-controls .weight-input :deep(.el-input__wrapper.is-focus) {
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.manual-add-controls .add-button {
    background: #667eea;
    border: none;
    border-radius: 8px;
    padding: 8px 16px;
    font-weight: 600;
    color: white;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 4px;
    height: 36px;
    font-size: 14px;
}

.manual-add-controls .add-button:hover:not(.is-disabled) {
    background: #5a6fd8;
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.4);
    transform: translateY(-1px);
}

.manual-add-controls .add-button:active:not(.is-disabled) {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.manual-add-controls .add-button.is-disabled {
    background: #d1d5db;
    color: #9ca3af;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 词汇限制提示 */
.word-limit-hint {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-top: 6px;
    padding: 8px 12px;
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 152, 0, 0.1) 100%);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 6px;
    font-size: 12px;
    color: #e67e22;
    font-weight: 500;
}

.word-limit-hint .el-icon {
    color: #f39c12;
    font-size: 14px;
}
