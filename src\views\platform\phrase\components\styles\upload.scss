/* 拖拽上传区域样式 */
.drag-upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 12px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    padding: 24px 20px;
    text-align: center;
    margin-bottom: 8px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    min-height: 130px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    margin-bottom: 30px;
}

.drag-upload-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 12px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.drag-upload-area:hover {
    border-color: #667eea;
    background: linear-gradient(135deg, #f0f4ff 0%, #e6f0ff 100%);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.12);
    transform: translateY(-2px);
}

.drag-upload-area:hover::before {
    opacity: 1;
}

.drag-upload-area.drag-over {
    border-color: #667eea;
    background: linear-gradient(135deg, #e6f0ff 0%, #dae6ff 100%);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.18);
    transform: translateY(-3px) scale(1.01);
}

.drag-upload-area.drag-over::before {
    opacity: 1;
}

.drag-upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    position: relative;
    z-index: 1;
    width: 100%;
}

.upload-icon {
    font-size: 28px;
    color: #9ca3af;
    margin-bottom: 6px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.drag-upload-area:hover .upload-icon,
.drag-upload-area.drag-over .upload-icon {
    color: #667eea;
    transform: scale(1.1);
    filter: drop-shadow(0 4px 8px rgba(102, 126, 234, 0.3));
}

.upload-text {
    color: #374151;
    text-align: center;
}

.upload-text p {
    margin: 2px 0;
    font-size: 14px;
    font-weight: 600;
    line-height: 1.4;
}

.upload-hint {
    font-size: 13px;
    color: #6b7280;
    font-weight: 500;
}

.upload-hint .el-button {
    font-weight: 700;
    color: #667eea;
    text-decoration: none;
    transition: color 0.3s ease;
}

.upload-hint .el-button:hover {
    color: #5a6fd8;
}

.format-hint {
    font-size: 12px;
    color: #9ca3af;
    font-style: italic;
    margin-top: 4px;
    font-weight: 400;
}

.drag-upload-area:hover .upload-text,
.drag-upload-area.drag-over .upload-text {
    color: #1f2937;
}

.drag-upload-area:hover .format-hint,
.drag-upload-area.drag-over .format-hint {
    color: #6b7280;
}

/* 上传操作按钮区域 */
.upload-actions {
    margin-top: 6px;
    display: flex;
    justify-content: center;
    width: 100%;
}

.download-test-btn {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(102, 126, 234, 0.3);
    color: #667eea;
    font-size: 12px;
    padding: 6px 12px;
    border-radius: 6px;
    transition: all 0.3s ease;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.download-test-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    border-color: #667eea;
    color: #5a6fd8;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.2);
}

.download-test-btn .el-icon {
    margin-right: 4px;
    font-size: 12px;
}

/* 拖拽状态下的按钮样式 */
.drag-upload-area:hover .download-test-btn,
.drag-upload-area.drag-over .download-test-btn {
    background: rgba(255, 255, 255, 0.95);
    border-color: #667eea;
    color: #667eea;
}

.drag-upload-area:hover .download-test-btn:hover,
.drag-upload-area.drag-over .download-test-btn:hover {
    background: rgba(102, 126, 234, 0.15);
    color: #5a6fd8;
}
